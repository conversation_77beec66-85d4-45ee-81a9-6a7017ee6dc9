"use client";

import React, { useState, useEffect, useCallback } from "react";
import { QuizFlowJSON, Question } from "@/types/qfjson";
import { assembleQuizQuestions, getLocalizedText } from "@/lib/utils/qfjson-parser";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import QuestionRenderer from "./QuestionRenderer";
import { calculatePercentage } from "@/lib/utils";

interface QuizRendererProps {
  quiz: QuizFlowJSON;
  onComplete?: (score: number, answers: Record<string, any>) => void;
  showAnswerButton?: boolean;
  answerDisplayConfig?: {
    showCorrectAnswer: boolean;
    showUserAnswer: boolean;
    showExplanationAfterAnswer: boolean;
    highlightCorrectAnswer: boolean;
    immediateAnswerFeedback: boolean;
  };
}

const QuizRenderer: React.FC<QuizRendererProps> = ({
  quiz,
  onComplete,
  showAnswerButton = false,
  answerDisplayConfig
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [score, setScore] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [questionAnswered, setQuestionAnswered] = useState<Record<string, boolean>>({});
  const [questionResults, setQuestionResults] = useState<Record<string, { isCorrect: boolean; userAnswer: any; correctAnswer: any }>>({});

  // Initialize quiz
  useEffect(() => {
    const assembledQuestions = assembleQuizQuestions(quiz);
    setQuestions(assembledQuestions);

    // Initialize time limit if specified
    if (quiz.quiz.metadata.time_limit_minutes) {
      setTimeRemaining(quiz.quiz.metadata.time_limit_minutes * 60); // Convert to seconds
    }
  }, [quiz]);

  const handleSubmitQuiz = useCallback(() => {
    // Calculate score
    let totalScore = 0;

    questions.forEach(question => {
      // This is a simplified scoring logic - in a real implementation,
      // you would need more complex logic based on question type
      const answer = answers[question.question_id];
      if (answer) {
        // For true/false questions
        if (question.type === "true_false" && answer === (question as any).correct_answer) {
          totalScore += question.points;
        }
        // For multiple choice with single answer
        else if (question.type === "multiple_choice" &&
                (question as any).single_correct_answer === true) {
          const correctOption = (question as any).options.find((opt: any) => opt.is_correct);
          if (correctOption && answer === correctOption.id) {
            totalScore += question.points;
          }
        }
        // For short answer questions
        else if (question.type === "short_answer") {
          const correctAnswers = (question as any).correct_answers;
          if (correctAnswers.includes(answer)) {
            totalScore += question.points;
          }
        }
        // Other question types would need their own scoring logic
      }
    });

    setScore(totalScore);
    setIsCompleted(true);

    if (onComplete) {
      onComplete(totalScore, answers);
    }
  }, [questions, answers, onComplete]);

  // Timer effect
  useEffect(() => {
    if (timeRemaining === null || timeRemaining <= 0 || isCompleted) return;

    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev === null || prev <= 1) {
          clearInterval(timer);
          if (prev === 1) {
            handleSubmitQuiz();
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [timeRemaining, isCompleted, handleSubmitQuiz]);

  // Function to check if an answer is correct
  const checkAnswerCorrectness = (question: Question, userAnswer: any) => {
    if (!userAnswer) return false;

    switch (question.type) {
      case "true_false": {
        const tfQuestion = question as any;
        return userAnswer === tfQuestion.correct_answer;
      }
      case "multiple_choice": {
        const mcQuestion = question as any;
        if (mcQuestion.single_correct_answer) {
          const correctOption = mcQuestion.options.find((opt: any) => opt.is_correct);
          return correctOption && userAnswer === correctOption.id;
        } else {
          // Multiple correct answers
          const correctOptions = mcQuestion.options.filter((opt: any) => opt.is_correct).map((opt: any) => opt.id);
          const userAnswers = Array.isArray(userAnswer) ? userAnswer : [userAnswer];
          return correctOptions.length === userAnswers.length &&
                 correctOptions.every((id: string) => userAnswers.includes(id));
        }
      }
      case "short_answer": {
        const saQuestion = question as any;
        const userAnswerStr = String(userAnswer).trim();
        return saQuestion.correct_answers.some((correct: string) => {
          if (saQuestion.case_sensitive === false) {
            return userAnswerStr.toLowerCase() === correct.toLowerCase();
          }
          return userAnswerStr === correct;
        });
      }
      case "matching": {
        const matchQuestion = question as any;
        const correctPairs = matchQuestion.correct_pairs;
        if (!Array.isArray(userAnswer)) return false;

        return correctPairs.every((pair: any) =>
          userAnswer.some((userPair: any) =>
            userPair.stem_id === pair.stem_id && userPair.option_id === pair.option_id
          )
        ) && userAnswer.length === correctPairs.length;
      }
      case "fill_in_the_blank": {
        const fibQuestion = question as any;
        if (!Array.isArray(userAnswer)) return false;

        return fibQuestion.blanks.every((blank: any, index: number) => {
          const userBlankAnswer = userAnswer[index];
          if (!userBlankAnswer) return false;

          return blank.correct_answers.some((correct: string) => {
            if (blank.case_sensitive === false) {
              return userBlankAnswer.toLowerCase() === correct.toLowerCase();
            }
            return userBlankAnswer === correct;
          });
        });
      }
      case "essay": {
        // Essay questions can't be automatically graded
        return false;
      }
      default:
        return false;
    }
  };

  // Function to get the correct answer for display
  const getCorrectAnswerForDisplay = (question: Question) => {
    switch (question.type) {
      case "true_false": {
        const tfQuestion = question as any;
        return tfQuestion.correct_answer;
      }
      case "multiple_choice": {
        const mcQuestion = question as any;
        if (mcQuestion.single_correct_answer) {
          const correctOption = mcQuestion.options.find((opt: any) => opt.is_correct);
          return correctOption?.id;
        } else {
          return mcQuestion.options.filter((opt: any) => opt.is_correct).map((opt: any) => opt.id);
        }
      }
      case "short_answer": {
        const saQuestion = question as any;
        return saQuestion.correct_answers[0]; // Show first correct answer
      }
      case "matching": {
        const matchQuestion = question as any;
        return matchQuestion.correct_pairs;
      }
      case "fill_in_the_blank": {
        const fibQuestion = question as any;
        return fibQuestion.blanks.map((blank: any) => blank.correct_answers[0]);
      }
      default:
        return null;
    }
  };

  const handleAnswerChange = (questionId: string, answer: any) => {
    const question = questions.find(q => q.question_id === questionId);
    if (!question) return;

    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));

    // If immediate feedback is enabled, check the answer and store the result
    if (answerDisplayConfig?.immediateAnswerFeedback && answer) {
      const isCorrect = checkAnswerCorrectness(question, answer);
      const correctAnswer = getCorrectAnswerForDisplay(question);

      setQuestionResults(prev => ({
        ...prev,
        [questionId]: {
          isCorrect,
          userAnswer: answer,
          correctAnswer
        }
      }));

      setQuestionAnswered(prev => ({
        ...prev,
        [questionId]: true
      }));
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };



  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (questions.length === 0) {
    return <div>Loading quiz...</div>;
  }

  const currentQuestion = questions[currentQuestionIndex];
  const metadata = quiz.quiz.metadata;
  const locale = metadata.locale || "en-US";

  if (isCompleted) {
    const percentage = calculatePercentage(score, questions.reduce((sum, q) => sum + q.points, 0));
    const isPassing = metadata.passing_score_percentage
      ? percentage >= metadata.passing_score_percentage
      : true;

    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Quiz Results</CardTitle>
          <CardDescription>
            {getLocalizedText(metadata.title, locale)}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-center">
              <h3 className="text-2xl font-bold">
                {isPassing ? "Congratulations!" : "Quiz Completed"}
              </h3>
              <p className="text-xl mt-2">
                Your score: {score} / {questions.reduce((sum, q) => sum + q.points, 0)} ({percentage}%)
              </p>
              {metadata.passing_score_percentage && (
                <p className="mt-2">
                  {isPassing
                    ? "You passed the quiz!"
                    : `You need ${metadata.passing_score_percentage}% to pass the quiz.`}
                </p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="justify-center">
          <Button onClick={() => window.location.reload()}>
            Take Another Quiz
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>{getLocalizedText(metadata.title, locale)}</CardTitle>
            <CardDescription>
              Question {currentQuestionIndex + 1} of {questions.length}
            </CardDescription>
          </div>
          {timeRemaining !== null && (
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Time Remaining</p>
              <p className="text-xl font-semibold">{formatTime(timeRemaining)}</p>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <QuestionRenderer
          question={currentQuestion}
          answer={answers[currentQuestion.question_id]}
          onAnswerChange={(answer) => handleAnswerChange(currentQuestion.question_id, answer)}
          locale={locale}
          showExplanation={isCompleted || (answerDisplayConfig?.showExplanationAfterAnswer && questionAnswered[currentQuestion.question_id])}
          showAnswerButton={showAnswerButton}
          answerDisplayConfig={answerDisplayConfig}
          questionResult={questionResults[currentQuestion.question_id]}
          isAnswered={questionAnswered[currentQuestion.question_id]}
        />
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePreviousQuestion}
          disabled={currentQuestionIndex === 0}
        >
          Previous
        </Button>
        <div className="flex gap-2">
          {currentQuestionIndex < questions.length - 1 ? (
            <Button onClick={handleNextQuestion}>Next</Button>
          ) : (
            <Button onClick={handleSubmitQuiz}>Submit Quiz</Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};

export default QuizRenderer;
